{"name": "cat_vod_nodejs_fastify", "version": "1.0.0", "description": "CatVodOpen nodejs config api server demo.", "type": "module", "scripts": {"dev": "cross-env DEV_HTTP_PORT=3006 nodemon --config nodemon.json --inspect --security-revert=CVE-2023-46809 src/dev.js", "_build": "rimraf dist && node esbuild.js && node esbuild-config.js", "build": "cross-env NODE_ENV=production npm run _build", "build:dbg": "cross-env NODE_ENV=development npm run _build", "build:config": "cross-env NODE_ENV=production node esbuild-config.js", "build:rollup(obsolete)": "rimraf dist && cross-env NODE_ENV=production node rollup.js && cross-env NODE_ENV=production node rollup-config.js", "build:rollup:config(obsolete)": "cross-env NODE_ENV=production node rollup-config.js", "deploy": "wrangler deploy"}, "author": "", "devDependencies": {"@babel/plugin-transform-runtime": "^7.23.9", "@babel/preset-env": "^7.23.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@types/node": "^20.11.14", "cross-env": "^7.0.3", "esbuild": "0.20.1", "esbuild-plugin-external-global": "^1.0.1", "eslint": "^8.57.0", "javascript-obfuscator": "^4.1.1", "less": "^4.2.2", "nodemon": "^3.0.3", "rimraf": "^5.0.5", "rollup": "^2.79.1"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "antd": "^5.24.3", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "esbuild-server": "^0.3.0", "fastify": "^4.26.0", "hls-parser": "^0.10.8", "iconv-lite": "^0.6.3", "json-bigint": "^1.0.0", "jsonpath-plus": "^10.3.0", "node-json-db": "^2.3.0", "node-rsa": "^1.1.1", "qrcode": "^1.5.4", "qs": "^6.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "wrangler": "^4.0.0"}}