.container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
}

.qrcodeCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
        width: 150px;
        height: 150px;
        cursor: pointer;
    }

    .btns {
        display: flex;
        justify-content: space-around;
        margin-top: 24px;
    }
}

.dynamic-delete-button {
    position: relative;
    top: 2px;
    margin: 0 8px;
    color: #555;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        color: #777;
    }

    &[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }
}