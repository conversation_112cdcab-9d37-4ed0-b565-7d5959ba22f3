// prettier-ignore
var JSONbig=function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var r,t,n,i,o,u={exports:{}},s={exports:{}},f={exports:{}},c=f.exports;function l(){return r||(r=1,e=f,function(r){var t,n=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,i=Math.ceil,o=Math.floor,u="[BigNumber Error] ",s=u+"Number primitive has more than 15 significant digits: ",f=1e14,c=14,l=9007199254740991,a=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],h=1e7,p=1e9;function g(e){var r=0|e;return e>0||e===r?r:r-1}function w(e){for(var r,t,n=1,i=e.length,o=e[0]+"";n<i;){for(r=e[n++]+"",t=c-r.length;t--;r="0"+r);o+=r}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function d(e,r){var t,n,i=e.c,o=r.c,u=e.s,s=r.s,f=e.e,c=r.e;if(!u||!s)return null;if(t=i&&!i[0],n=o&&!o[0],t||n)return t?n?0:-s:u;if(u!=s)return u;if(t=u<0,n=f==c,!i||!o)return n?0:!i^t?1:-1;if(!n)return f>c^t?1:-1;for(s=(f=i.length)<(c=o.length)?f:c,u=0;u<s;u++)if(i[u]!=o[u])return i[u]>o[u]^t?1:-1;return f==c?0:f>c^t?1:-1}function v(e,r,t,n){if(e<r||e>t||e!==o(e))throw Error(u+(n||"Argument")+("number"==typeof e?e<r||e>t?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function y(e){var r=e.c.length-1;return g(e.e/c)==r&&e.c[r]%2!=0}function m(e,r){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(r<0?"e":"e+")+r}function b(e,r,t){var n,i;if(r<0){for(i=t+".";++r;i+=t);e=i+e}else if(++r>(n=e.length)){for(i=t,r-=n;--r;i+=t);e+=i}else r<n&&(e=e.slice(0,r)+"."+e.slice(r));return e}t=function e(r){var t,A,N,O,E,x,B,S,I,P,_=q.prototype={constructor:q,toString:null,valueOf:null},j=new q(1),D=20,R=4,F=-7,L=21,U=-1e7,C=1e7,T=!1,M=1,k=0,G={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:"\xa0",suffix:""},$="0123456789abcdefghijklmnopqrstuvwxyz";function q(e,r){var t,i,u,f,a,h,p,g,w=this;if(!(w instanceof q))return new q(e,r);if(null==r){if(e&&!0===e._isBigNumber)return w.s=e.s,void(!e.c||e.e>C?w.c=w.e=null:e.e<U?w.c=[w.e=0]:(w.e=e.e,w.c=e.c.slice()));if((h="number"==typeof e)&&0*e==0){if(w.s=1/e<0?(e=-e,-1):1,e===~~e){for(f=0,a=e;a>=10;a/=10,f++);return void(f>C?w.c=w.e=null:(w.e=f,w.c=[e]))}g=String(e)}else{if(!n.test(g=String(e)))return N(w,g,h);w.s=45==g.charCodeAt(0)?(g=g.slice(1),-1):1}(f=g.indexOf("."))>-1&&(g=g.replace(".","")),(a=g.search(/e/i))>0?(f<0&&(f=a),f+=+g.slice(a+1),g=g.substring(0,a)):f<0&&(f=g.length)}else{if(v(r,2,$.length,"Base"),10==r)return J(w=new q(e),D+w.e+1,R);if(g=String(e),h="number"==typeof e){if(0*e!=0)return N(w,g,h,r);if(w.s=1/e<0?(g=g.slice(1),-1):1,q.DEBUG&&g.replace(/^0\.0*|\./,"").length>15)throw Error(s+e)}else w.s=45===g.charCodeAt(0)?(g=g.slice(1),-1):1;for(t=$.slice(0,r),f=a=0,p=g.length;a<p;a++)if(t.indexOf(i=g.charAt(a))<0){if("."==i){if(a>f){f=p;continue}}else if(!u&&(g==g.toUpperCase()&&(g=g.toLowerCase())||g==g.toLowerCase()&&(g=g.toUpperCase()))){u=!0,a=-1,f=0;continue}return N(w,String(e),h,r)}h=!1,(f=(g=A(g,r,10,w.s)).indexOf("."))>-1?g=g.replace(".",""):f=g.length}for(a=0;48===g.charCodeAt(a);a++);for(p=g.length;48===g.charCodeAt(--p););if(g=g.slice(a,++p)){if(p-=a,h&&q.DEBUG&&p>15&&(e>l||e!==o(e)))throw Error(s+w.s*e);if((f=f-a-1)>C)w.c=w.e=null;else if(f<U)w.c=[w.e=0];else{if(w.e=f,w.c=[],a=(f+1)%c,f<0&&(a+=c),a<p){for(a&&w.c.push(+g.slice(0,a)),p-=c;a<p;)w.c.push(+g.slice(a,a+=c));a=c-(g=g.slice(a)).length}else a-=p;for(;a--;g+="0");w.c.push(+g)}}else w.c=[w.e=0]}function z(e,r,t,n){var i,o,u,s,f;if(null==t?t=R:v(t,0,8),!e.c)return e.toString();if(i=e.c[0],u=e.e,null==r)f=w(e.c),f=1==n||2==n&&(u<=F||u>=L)?m(f,u):b(f,u,"0");else if(o=(e=J(new q(e),r,t)).e,s=(f=w(e.c)).length,1==n||2==n&&(r<=o||o<=F)){for(;s<r;f+="0",s++);f=m(f,o)}else if(r-=u,f=b(f,o,"0"),o+1>s){if(--r>0)for(f+=".";r--;f+="0");}else if((r+=o-s)>0)for(o+1==s&&(f+=".");r--;f+="0");return e.s<0&&i?"-"+f:f}function H(e,r){for(var t,n=1,i=new q(e[0]);n<e.length;n++){if(!(t=new q(e[n])).s){i=t;break}r.call(i,t)&&(i=t)}return i}function V(e,r,t){for(var n=1,i=r.length;!r[--i];r.pop());for(i=r[0];i>=10;i/=10,n++);return(t=n+t*c-1)>C?e.c=e.e=null:t<U?e.c=[e.e=0]:(e.e=t,e.c=r),e}function J(e,r,t,n){var u,s,l,h,p,g,w,d=e.c,v=a;if(d){e:{for(u=1,h=d[0];h>=10;h/=10,u++);if((s=r-u)<0)s+=c,l=r,w=(p=d[g=0])/v[u-l-1]%10|0;else if((g=i((s+1)/c))>=d.length){if(!n)break e;for(;d.length<=g;d.push(0));p=w=0,u=1,l=(s%=c)-c+1}else{for(p=h=d[g],u=1;h>=10;h/=10,u++);w=(l=(s%=c)-c+u)<0?0:p/v[u-l-1]%10|0}if(n=n||r<0||null!=d[g+1]||(l<0?p:p%v[u-l-1]),n=t<4?(w||n)&&(0==t||t==(e.s<0?3:2)):w>5||5==w&&(4==t||n||6==t&&(s>0?l>0?p/v[u-l]:0:d[g-1])%10&1||t==(e.s<0?8:7)),r<1||!d[0])return d.length=0,n?(r-=e.e+1,d[0]=v[(c-r%c)%c],e.e=-r||0):d[0]=e.e=0,e;if(0==s?(d.length=g,h=1,g--):(d.length=g+1,h=v[c-s],d[g]=l>0?o(p/v[u-l]%v[l])*h:0),n)for(;;){if(0==g){for(s=1,l=d[0];l>=10;l/=10,s++);for(l=d[0]+=h,h=1;l>=10;l/=10,h++);s!=h&&(e.e++,d[0]==f&&(d[0]=1));break}if(d[g]+=h,d[g]!=f)break;d[g--]=0,h=1}for(s=d.length;0===d[--s];d.pop());}e.e>C?e.c=e.e=null:e.e<U&&(e.c=[e.e=0])}return e}function W(e){var r,t=e.e;return null===t?e.toString():(r=w(e.c),r=t<=F||t>=L?m(r,t):b(r,t,"0"),e.s<0?"-"+r:r)}return q.clone=e,q.ROUND_UP=0,q.ROUND_DOWN=1,q.ROUND_CEIL=2,q.ROUND_FLOOR=3,q.ROUND_HALF_UP=4,q.ROUND_HALF_DOWN=5,q.ROUND_HALF_EVEN=6,q.ROUND_HALF_CEIL=7,q.ROUND_HALF_FLOOR=8,q.EUCLID=9,q.config=q.set=function(e){var r,t;if(null!=e){if("object"!=typeof e)throw Error(u+"Object expected: "+e);if(e.hasOwnProperty(r="DECIMAL_PLACES")&&(v(t=e[r],0,p,r),D=t),e.hasOwnProperty(r="ROUNDING_MODE")&&(v(t=e[r],0,8,r),R=t),e.hasOwnProperty(r="EXPONENTIAL_AT")&&((t=e[r])&&t.pop?(v(t[0],-p,0,r),v(t[1],0,p,r),F=t[0],L=t[1]):(v(t,-p,p,r),F=-(L=t<0?-t:t))),e.hasOwnProperty(r="RANGE"))if((t=e[r])&&t.pop)v(t[0],-p,-1,r),v(t[1],1,p,r),U=t[0],C=t[1];else{if(v(t,-p,p,r),!t)throw Error(u+r+" cannot be zero: "+t);U=-(C=t<0?-t:t)}if(e.hasOwnProperty(r="CRYPTO")){if((t=e[r])!==!!t)throw Error(u+r+" not true or false: "+t);if(t){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw T=!t,Error(u+"crypto unavailable");T=t}else T=t}if(e.hasOwnProperty(r="MODULO_MODE")&&(v(t=e[r],0,9,r),M=t),e.hasOwnProperty(r="POW_PRECISION")&&(v(t=e[r],0,p,r),k=t),e.hasOwnProperty(r="FORMAT")){if("object"!=typeof(t=e[r]))throw Error(u+r+" not an object: "+t);G=t}if(e.hasOwnProperty(r="ALPHABET")){if("string"!=typeof(t=e[r])||/^.$|[+-.\s]|(.).*\1/.test(t))throw Error(u+r+" invalid: "+t);$=t}}return{DECIMAL_PLACES:D,ROUNDING_MODE:R,EXPONENTIAL_AT:[F,L],RANGE:[U,C],CRYPTO:T,MODULO_MODE:M,POW_PRECISION:k,FORMAT:G,ALPHABET:$}},q.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!q.DEBUG)return!0;var r,t,n=e.c,i=e.e,s=e.s;e:if("[object Array]"=={}.toString.call(n)){if((1===s||-1===s)&&i>=-p&&i<=p&&i===o(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break e}if((r=(i+1)%c)<1&&(r+=c),String(n[0]).length==r){for(r=0;r<n.length;r++)if((t=n[r])<0||t>=f||t!==o(t))break e;if(0!==t)return!0}}}else if(null===n&&null===i&&(null===s||1===s||-1===s))return!0;throw Error(u+"Invalid BigNumber: "+e)},q.maximum=q.max=function(){return H(arguments,_.lt)},q.minimum=q.min=function(){return H(arguments,_.gt)},q.random=(O=9007199254740992,E=Math.random()*O&2097151?function(){return o(Math.random()*O)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var r,t,n,s,f,l=0,h=[],g=new q(j);if(null==e?e=D:v(e,0,p),s=i(e/c),T)if(crypto.getRandomValues){for(r=crypto.getRandomValues(new Uint32Array(s*=2));l<s;)(f=131072*r[l]+(r[l+1]>>>11))>=9e15?(t=crypto.getRandomValues(new Uint32Array(2)),r[l]=t[0],r[l+1]=t[1]):(h.push(f%1e14),l+=2);l=s/2}else{if(!crypto.randomBytes)throw T=!1,Error(u+"crypto unavailable");for(r=crypto.randomBytes(s*=7);l<s;)(f=281474976710656*(31&r[l])+1099511627776*r[l+1]+4294967296*r[l+2]+16777216*r[l+3]+(r[l+4]<<16)+(r[l+5]<<8)+r[l+6])>=9e15?crypto.randomBytes(7).copy(r,l):(h.push(f%1e14),l+=7);l=s/7}if(!T)for(;l<s;)(f=E())<9e15&&(h[l++]=f%1e14);for(s=h[--l],e%=c,s&&e&&(f=a[c-e],h[l]=o(s/f)*f);0===h[l];h.pop(),l--);if(l<0)h=[n=0];else{for(n=-1;0===h[0];h.splice(0,1),n-=c);for(l=1,f=h[0];f>=10;f/=10,l++);l<c&&(n-=c-l)}return g.e=n,g.c=h,g}),q.sum=function(){for(var e=1,r=arguments,t=new q(r[0]);e<r.length;)t=t.plus(r[e++]);return t},A=function(){var e="0123456789";function r(e,r,t,n){for(var i,o,u=[0],s=0,f=e.length;s<f;){for(o=u.length;o--;u[o]*=r);for(u[0]+=n.indexOf(e.charAt(s++)),i=0;i<u.length;i++)u[i]>t-1&&(null==u[i+1]&&(u[i+1]=0),u[i+1]+=u[i]/t|0,u[i]%=t)}return u.reverse()}return function(n,i,o,u,s){var f,c,l,a,h,p,g,d,v=n.indexOf("."),y=D,m=R;for(v>=0&&(a=k,k=0,n=n.replace(".",""),p=(d=new q(i)).pow(n.length-v),k=a,d.c=r(b(w(p.c),p.e,"0"),10,o,e),d.e=d.c.length),l=a=(g=r(n,i,o,s?(f=$,e):(f=e,$))).length;0==g[--a];g.pop());if(!g[0])return f.charAt(0);if(v<0?--l:(p.c=g,p.e=l,p.s=u,g=(p=t(p,d,y,m,o)).c,h=p.r,l=p.e),v=g[c=l+y+1],a=o/2,h=h||c<0||null!=g[c+1],h=m<4?(null!=v||h)&&(0==m||m==(p.s<0?3:2)):v>a||v==a&&(4==m||h||6==m&&1&g[c-1]||m==(p.s<0?8:7)),c<1||!g[0])n=h?b(f.charAt(1),-y,f.charAt(0)):f.charAt(0);else{if(g.length=c,h)for(--o;++g[--c]>o;)g[c]=0,c||(++l,g=[1].concat(g));for(a=g.length;!g[--a];);for(v=0,n="";v<=a;n+=f.charAt(g[v++]));n=b(n,l,f.charAt(0))}return n}}(),t=function(){function e(e,r,t){var n,i,o,u,s=0,f=e.length,c=r%h,l=r/h|0;for(e=e.slice();f--;)s=((i=c*(o=e[f]%h)+(n=l*o+(u=e[f]/h|0)*c)%h*h+s)/t|0)+(n/h|0)+l*u,e[f]=i%t;return s&&(e=[s].concat(e)),e}function r(e,r,t,n){var i,o;if(t!=n)o=t>n?1:-1;else for(i=o=0;i<t;i++)if(e[i]!=r[i]){o=e[i]>r[i]?1:-1;break}return o}function t(e,r,t,n){for(var i=0;t--;)e[t]-=i,i=e[t]<r[t]?1:0,e[t]=i*n+e[t]-r[t];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(n,i,u,s,l){var a,h,p,w,d,v,y,m,b,A,N,O,E,x,B,S,I,P=n.s==i.s?1:-1,_=n.c,j=i.c;if(!(_&&_[0]&&j&&j[0]))return new q(n.s&&i.s&&(_?!j||_[0]!=j[0]:j)?_&&0==_[0]||!j?0*P:P/0:NaN);for(b=(m=new q(P)).c=[],P=u+(h=n.e-i.e)+1,l||(l=f,h=g(n.e/c)-g(i.e/c),P=P/c|0),p=0;j[p]==(_[p]||0);p++);if(j[p]>(_[p]||0)&&h--,P<0)b.push(1),w=!0;else{for(x=_.length,S=j.length,p=0,P+=2,(d=o(l/(j[0]+1)))>1&&(j=e(j,d,l),_=e(_,d,l),S=j.length,x=_.length),E=S,N=(A=_.slice(0,S)).length;N<S;A[N++]=0);I=j.slice(),I=[0].concat(I),B=j[0],j[1]>=l/2&&B++;do{if(d=0,(a=r(j,A,S,N))<0){if(O=A[0],S!=N&&(O=O*l+(A[1]||0)),(d=o(O/B))>1)for(d>=l&&(d=l-1),y=(v=e(j,d,l)).length,N=A.length;1==r(v,A,y,N);)d--,t(v,S<y?I:j,y,l),y=v.length,a=1;else 0==d&&(a=d=1),y=(v=j.slice()).length;if(y<N&&(v=[0].concat(v)),t(A,v,N,l),N=A.length,-1==a)for(;r(j,A,S,N)<1;)d++,t(A,S<N?I:j,N,l),N=A.length}else 0===a&&(d++,A=[0]);b[p++]=d,A[0]?A[N++]=_[E]||0:(A=[_[E]],N=1)}while((E++<x||null!=A[0])&&P--);w=null!=A[0],b[0]||b.splice(0,1)}if(l==f){for(p=1,P=b[0];P>=10;P/=10,p++);J(m,u+(m.e=p+h*c-1)+1,s,w)}else m.e=h,m.r=+w;return m}}(),x=/^(-?)0([xbo])(?=\w[\w.]*$)/i,B=/^([^.]+)\.$/,S=/^\.([^.]+)$/,I=/^-?(Infinity|NaN)$/,P=/^\s*\+(?=[\w.])|^\s+|\s+$/g,N=function(e,r,t,n){var i,o=t?r:r.replace(P,"");if(I.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!t&&(o=o.replace(x,(function(e,r,t){return i="x"==(t=t.toLowerCase())?16:"b"==t?2:8,n&&n!=i?e:r})),n&&(i=n,o=o.replace(B,"$1").replace(S,"0.$1")),r!=o))return new q(o,i);if(q.DEBUG)throw Error(u+"Not a"+(n?" base "+n:"")+" number: "+r);e.s=null}e.c=e.e=null},_.absoluteValue=_.abs=function(){var e=new q(this);return e.s<0&&(e.s=1),e},_.comparedTo=function(e,r){return d(this,new q(e,r))},_.decimalPlaces=_.dp=function(e,r){var t,n,i,o=this;if(null!=e)return v(e,0,p),null==r?r=R:v(r,0,8),J(new q(o),e+o.e+1,r);if(!(t=o.c))return null;if(n=((i=t.length-1)-g(this.e/c))*c,i=t[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},_.dividedBy=_.div=function(e,r){return t(this,new q(e,r),D,R)},_.dividedToIntegerBy=_.idiv=function(e,r){return t(this,new q(e,r),0,1)},_.exponentiatedBy=_.pow=function(e,r){var t,n,s,f,l,a,h,p,g=this;if((e=new q(e)).c&&!e.isInteger())throw Error(u+"Exponent not an integer: "+W(e));if(null!=r&&(r=new q(r)),l=e.e>14,!g.c||!g.c[0]||1==g.c[0]&&!g.e&&1==g.c.length||!e.c||!e.c[0])return p=new q(Math.pow(+W(g),l?2-y(e):+W(e))),r?p.mod(r):p;if(a=e.s<0,r){if(r.c?!r.c[0]:!r.s)return new q(NaN);(n=!a&&g.isInteger()&&r.isInteger())&&(g=g.mod(r))}else{if(e.e>9&&(g.e>0||g.e<-1||(0==g.e?g.c[0]>1||l&&g.c[1]>=24e7:g.c[0]<8e13||l&&g.c[0]<=9999975e7)))return f=g.s<0&&y(e)?-0:0,g.e>-1&&(f=1/f),new q(a?1/f:f);k&&(f=i(k/c+2))}for(l?(t=new q(.5),a&&(e.s=1),h=y(e)):h=(s=Math.abs(+W(e)))%2,p=new q(j);;){if(h){if(!(p=p.times(g)).c)break;f?p.c.length>f&&(p.c.length=f):n&&(p=p.mod(r))}if(s){if(0===(s=o(s/2)))break;h=s%2}else if(J(e=e.times(t),e.e+1,1),e.e>14)h=y(e);else{if(0==(s=+W(e)))break;h=s%2}g=g.times(g),f?g.c&&g.c.length>f&&(g.c.length=f):n&&(g=g.mod(r))}return n?p:(a&&(p=j.div(p)),r?p.mod(r):f?J(p,k,R,void 0):p)},_.integerValue=function(e){var r=new q(this);return null==e?e=R:v(e,0,8),J(r,r.e+1,e)},_.isEqualTo=_.eq=function(e,r){return 0===d(this,new q(e,r))},_.isFinite=function(){return!!this.c},_.isGreaterThan=_.gt=function(e,r){return d(this,new q(e,r))>0},_.isGreaterThanOrEqualTo=_.gte=function(e,r){return 1===(r=d(this,new q(e,r)))||0===r},_.isInteger=function(){return!!this.c&&g(this.e/c)>this.c.length-2},_.isLessThan=_.lt=function(e,r){return d(this,new q(e,r))<0},_.isLessThanOrEqualTo=_.lte=function(e,r){return-1===(r=d(this,new q(e,r)))||0===r},_.isNaN=function(){return!this.s},_.isNegative=function(){return this.s<0},_.isPositive=function(){return this.s>0},_.isZero=function(){return!!this.c&&0==this.c[0]},_.minus=function(e,r){var t,n,i,o,u=this,s=u.s;if(r=(e=new q(e,r)).s,!s||!r)return new q(NaN);if(s!=r)return e.s=-r,u.plus(e);var l=u.e/c,a=e.e/c,h=u.c,p=e.c;if(!l||!a){if(!h||!p)return h?(e.s=-r,e):new q(p?u:NaN);if(!h[0]||!p[0])return p[0]?(e.s=-r,e):new q(h[0]?u:3==R?-0:0)}if(l=g(l),a=g(a),h=h.slice(),s=l-a){for((o=s<0)?(s=-s,i=h):(a=l,i=p),i.reverse(),r=s;r--;i.push(0));i.reverse()}else for(n=(o=(s=h.length)<(r=p.length))?s:r,s=r=0;r<n;r++)if(h[r]!=p[r]){o=h[r]<p[r];break}if(o&&(i=h,h=p,p=i,e.s=-e.s),(r=(n=p.length)-(t=h.length))>0)for(;r--;h[t++]=0);for(r=f-1;n>s;){if(h[--n]<p[n]){for(t=n;t&&!h[--t];h[t]=r);--h[t],h[n]+=f}h[n]-=p[n]}for(;0==h[0];h.splice(0,1),--a);return h[0]?V(e,h,a):(e.s=3==R?-1:1,e.c=[e.e=0],e)},_.modulo=_.mod=function(e,r){var n,i,o=this;return e=new q(e,r),!o.c||!e.s||e.c&&!e.c[0]?new q(NaN):!e.c||o.c&&!o.c[0]?new q(o):(9==M?(i=e.s,e.s=1,n=t(o,e,0,3),e.s=i,n.s*=i):n=t(o,e,0,M),(e=o.minus(n.times(e))).c[0]||1!=M||(e.s=o.s),e)},_.multipliedBy=_.times=function(e,r){var t,n,i,o,u,s,l,a,p,w,d,v,y,m,b,A=this,N=A.c,O=(e=new q(e,r)).c;if(!(N&&O&&N[0]&&O[0]))return!A.s||!e.s||N&&!N[0]&&!O||O&&!O[0]&&!N?e.c=e.e=e.s=null:(e.s*=A.s,N&&O?(e.c=[0],e.e=0):e.c=e.e=null),e;for(n=g(A.e/c)+g(e.e/c),e.s*=A.s,(l=N.length)<(w=O.length)&&(y=N,N=O,O=y,i=l,l=w,w=i),i=l+w,y=[];i--;y.push(0));for(m=f,b=h,i=w;--i>=0;){for(t=0,d=O[i]%b,v=O[i]/b|0,o=i+(u=l);o>i;)t=((a=d*(a=N[--u]%b)+(s=v*a+(p=N[u]/b|0)*d)%b*b+y[o]+t)/m|0)+(s/b|0)+v*p,y[o--]=a%m;y[o]=t}return t?++n:y.splice(0,1),V(e,y,n)},_.negated=function(){var e=new q(this);return e.s=-e.s||null,e},_.plus=function(e,r){var t,n=this,i=n.s;if(r=(e=new q(e,r)).s,!i||!r)return new q(NaN);if(i!=r)return e.s=-r,n.minus(e);var o=n.e/c,u=e.e/c,s=n.c,l=e.c;if(!o||!u){if(!s||!l)return new q(i/0);if(!s[0]||!l[0])return l[0]?e:new q(s[0]?n:0*i)}if(o=g(o),u=g(u),s=s.slice(),i=o-u){for(i>0?(u=o,t=l):(i=-i,t=s),t.reverse();i--;t.push(0));t.reverse()}for((i=s.length)-(r=l.length)<0&&(t=l,l=s,s=t,r=i),i=0;r;)i=(s[--r]=s[r]+l[r]+i)/f|0,s[r]=f===s[r]?0:s[r]%f;return i&&(s=[i].concat(s),++u),V(e,s,u)},_.precision=_.sd=function(e,r){var t,n,i,o=this;if(null!=e&&e!==!!e)return v(e,1,p),null==r?r=R:v(r,0,8),J(new q(o),e,r);if(!(t=o.c))return null;if(n=(i=t.length-1)*c+1,i=t[i]){for(;i%10==0;i/=10,n--);for(i=t[0];i>=10;i/=10,n++);}return e&&o.e+1>n&&(n=o.e+1),n},_.shiftedBy=function(e){return v(e,-9007199254740991,l),this.times("1e"+e)},_.squareRoot=_.sqrt=function(){var e,r,n,i,o,u=this,s=u.c,f=u.s,c=u.e,l=D+4,a=new q("0.5");if(1!==f||!s||!s[0])return new q(!f||f<0&&(!s||s[0])?NaN:s?u:1/0);if(0==(f=Math.sqrt(+W(u)))||f==1/0?(((r=w(s)).length+c)%2==0&&(r+="0"),f=Math.sqrt(+r),c=g((c+1)/2)-(c<0||c%2),n=new q(r=f==1/0?"1e"+c:(r=f.toExponential()).slice(0,r.indexOf("e")+1)+c)):n=new q(f+""),n.c[0])for((f=(c=n.e)+l)<3&&(f=0);;)if(o=n,n=a.times(o.plus(t(u,o,l,1))),w(o.c).slice(0,f)===(r=w(n.c)).slice(0,f)){if(n.e<c&&--f,"9999"!=(r=r.slice(f-3,f+1))&&(i||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(J(n,n.e+D+2,1),e=!n.times(n).eq(u));break}if(!i&&(J(o,o.e+D+2,0),o.times(o).eq(u))){n=o;break}l+=4,f+=4,i=1}return J(n,n.e+D+1,R,e)},_.toExponential=function(e,r){return null!=e&&(v(e,0,p),e++),z(this,e,r,1)},_.toFixed=function(e,r){return null!=e&&(v(e,0,p),e=e+this.e+1),z(this,e,r)},_.toFormat=function(e,r,t){var n,i=this;if(null==t)null!=e&&r&&"object"==typeof r?(t=r,r=null):e&&"object"==typeof e?(t=e,e=r=null):t=G;else if("object"!=typeof t)throw Error(u+"Argument not an object: "+t);if(n=i.toFixed(e,r),i.c){var o,s=n.split("."),f=+t.groupSize,c=+t.secondaryGroupSize,l=t.groupSeparator||"",a=s[0],h=s[1],p=i.s<0,g=p?a.slice(1):a,w=g.length;if(c&&(o=f,f=c,c=o,w-=o),f>0&&w>0){for(o=w%f||f,a=g.substr(0,o);o<w;o+=f)a+=l+g.substr(o,f);c>0&&(a+=l+g.slice(o)),p&&(a="-"+a)}n=h?a+(t.decimalSeparator||"")+((c=+t.fractionGroupSize)?h.replace(new RegExp("\\d{"+c+"}\\B","g"),"$&"+(t.fractionGroupSeparator||"")):h):a}return(t.prefix||"")+n+(t.suffix||"")},_.toFraction=function(e){var r,n,i,o,s,f,l,h,p,g,d,v,y=this,m=y.c;if(null!=e&&(!(l=new q(e)).isInteger()&&(l.c||1!==l.s)||l.lt(j)))throw Error(u+"Argument "+(l.isInteger()?"out of range: ":"not an integer: ")+W(l));if(!m)return new q(y);for(r=new q(j),p=n=new q(j),i=h=new q(j),v=w(m),s=r.e=v.length-y.e-1,r.c[0]=a[(f=s%c)<0?c+f:f],e=!e||l.comparedTo(r)>0?s>0?r:p:l,f=C,C=1/0,l=new q(v),h.c[0]=0;g=t(l,r,0,1),1!=(o=n.plus(g.times(i))).comparedTo(e);)n=i,i=o,p=h.plus(g.times(o=p)),h=o,r=l.minus(g.times(o=r)),l=o;return o=t(e.minus(n),i,0,1),h=h.plus(o.times(p)),n=n.plus(o.times(i)),h.s=p.s=y.s,d=t(p,i,s*=2,R).minus(y).abs().comparedTo(t(h,n,s,R).minus(y).abs())<1?[p,i]:[h,n],C=f,d},_.toNumber=function(){return+W(this)},_.toPrecision=function(e,r){return null!=e&&v(e,1,p),z(this,e,r,2)},_.toString=function(e){var r,t=this,n=t.s,i=t.e;return null===i?n?(r="Infinity",n<0&&(r="-"+r)):r="NaN":(null==e?r=i<=F||i>=L?m(w(t.c),i):b(w(t.c),i,"0"):10===e?r=b(w((t=J(new q(t),D+i+1,R)).c),t.e,"0"):(v(e,2,$.length,"Base"),r=A(b(w(t.c),i,"0"),10,e,n,!0)),n<0&&t.c[0]&&(r="-"+r)),r},_.valueOf=_.toJSON=function(){return W(this)},_._isBigNumber=!0,null!=r&&q.set(r),q}(),t.default=t.BigNumber=t,e.exports?e.exports=t:(r||(r="undefined"!=typeof self&&self?self:window),r.BigNumber=t)}(c)),f.exports;var e}function a(){return t||(t=1,e=s,r=l(),n=e.exports,function(){var e,t,i,o=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,u={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};function s(e){return o.lastIndex=0,o.test(e)?'"'+e.replace(o,(function(e){var r=u[e];return"string"==typeof r?r:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'}function f(n,o){var u,c,l,a,h,p=e,g=o[n],w=null!=g&&(g instanceof r||r.isBigNumber(g));switch(g&&"object"==typeof g&&"function"==typeof g.toJSON&&(g=g.toJSON(n)),"function"==typeof i&&(g=i.call(o,n,g)),typeof g){case"string":return w?g:s(g);case"number":return isFinite(g)?String(g):"null";case"boolean":case"null":case"bigint":return String(g);case"object":if(!g)return"null";if(e+=t,h=[],"[object Array]"===Object.prototype.toString.apply(g)){for(a=g.length,u=0;u<a;u+=1)h[u]=f(u,g)||"null";return l=0===h.length?"[]":e?"[\n"+e+h.join(",\n"+e)+"\n"+p+"]":"["+h.join(",")+"]",e=p,l}if(i&&"object"==typeof i)for(a=i.length,u=0;u<a;u+=1)"string"==typeof i[u]&&(l=f(c=i[u],g))&&h.push(s(c)+(e?": ":":")+l);else Object.keys(g).forEach((function(r){var t=f(r,g);t&&h.push(s(r)+(e?": ":":")+t)}));return l=0===h.length?"{}":e?"{\n"+e+h.join(",\n"+e)+"\n"+p+"}":"{"+h.join(",")+"}",e=p,l}}"function"!=typeof n.stringify&&(n.stringify=function(r,n,o){var u;if(e="",t="","number"==typeof o)for(u=0;u<o;u+=1)t+=" ";else"string"==typeof o&&(t=o);if(i=n,n&&"function"!=typeof n&&("object"!=typeof n||"number"!=typeof n.length))throw new Error("JSON.stringify");return f("",{"":r})})}()),s.exports;var e,r,n}function h(){if(i)return n;i=1;var e=null;const r=/(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])/,t=/(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)/;return n=function(n){var i={strict:!1,storeAsString:!1,alwaysParseAsBig:!1,useNativeBigInt:!1,protoAction:"error",constructorAction:"error"};if(null!=n){if(!0===n.strict&&(i.strict=!0),!0===n.storeAsString&&(i.storeAsString=!0),i.alwaysParseAsBig=!0===n.alwaysParseAsBig&&n.alwaysParseAsBig,i.useNativeBigInt=!0===n.useNativeBigInt&&n.useNativeBigInt,void 0!==n.constructorAction){if("error"!==n.constructorAction&&"ignore"!==n.constructorAction&&"preserve"!==n.constructorAction)throw new Error(`Incorrect value for constructorAction option, must be "error", "ignore" or undefined but passed ${n.constructorAction}`);i.constructorAction=n.constructorAction}if(void 0!==n.protoAction){if("error"!==n.protoAction&&"ignore"!==n.protoAction&&"preserve"!==n.protoAction)throw new Error(`Incorrect value for protoAction option, must be "error", "ignore" or undefined but passed ${n.protoAction}`);i.protoAction=n.protoAction}}var o,u,s,f,c={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"},a=function(e){throw{name:"SyntaxError",message:e,at:o,text:s}},h=function(e){return e&&e!==u&&a("Expected '"+e+"' instead of '"+u+"'"),u=s.charAt(o),o+=1,u},p=function(){var r,t="";for("-"===u&&(t="-",h("-"));u>="0"&&u<="9";)t+=u,h();if("."===u)for(t+=".";h()&&u>="0"&&u<="9";)t+=u;if("e"===u||"E"===u)for(t+=u,h(),"-"!==u&&"+"!==u||(t+=u,h());u>="0"&&u<="9";)t+=u,h();if(r=+t,isFinite(r))return null==e&&(e=l()),Number.isSafeInteger(r)?i.alwaysParseAsBig?i.useNativeBigInt?BigInt(r):new e(r):r:i.storeAsString?t:/[\.eE]/.test(t)?r:i.useNativeBigInt?BigInt(t):new e(t);a("Bad number")},g=function(){var e,r,t,n="";if('"'===u)for(var i=o;h();){if('"'===u)return o-1>i&&(n+=s.substring(i,o-1)),h(),n;if("\\"===u){if(o-1>i&&(n+=s.substring(i,o-1)),h(),"u"===u){for(t=0,r=0;r<4&&(e=parseInt(h(),16),isFinite(e));r+=1)t=16*t+e;n+=String.fromCharCode(t)}else{if("string"!=typeof c[u])break;n+=c[u]}i=o}}a("Bad string")},w=function(){for(;u&&u<=" ";)h()};return f=function(){switch(w(),u){case"{":return function(){var e,n=Object.create(null);if("{"===u){if(h("{"),w(),"}"===u)return h("}"),n;for(;u;){if(e=g(),w(),h(":"),!0===i.strict&&Object.hasOwnProperty.call(n,e)&&a('Duplicate key "'+e+'"'),!0===r.test(e)?"error"===i.protoAction?a("Object contains forbidden prototype property"):"ignore"===i.protoAction?f():n[e]=f():!0===t.test(e)?"error"===i.constructorAction?a("Object contains forbidden constructor property"):"ignore"===i.constructorAction?f():n[e]=f():n[e]=f(),w(),"}"===u)return h("}"),n;h(","),w()}}a("Bad object")}();case"[":return function(){var e=[];if("["===u){if(h("["),w(),"]"===u)return h("]"),e;for(;u;){if(e.push(f()),w(),"]"===u)return h("]"),e;h(","),w()}}a("Bad array")}();case'"':return g();case"-":return p();default:return u>="0"&&u<="9"?p():function(){switch(u){case"t":return h("t"),h("r"),h("u"),h("e"),!0;case"f":return h("f"),h("a"),h("l"),h("s"),h("e"),!1;case"n":return h("n"),h("u"),h("l"),h("l"),null}a("Unexpected '"+u+"'")}()}},function(e,r){var t;return s=e+"",o=0,u=" ",t=f(),w(),u&&a("Syntax error"),"function"==typeof r?function e(t,n){var i,o=t[n];return o&&"object"==typeof o&&Object.keys(o).forEach((function(r){void 0!==(i=e(o,r))?o[r]=i:delete o[r]})),r.call(t,n,o)}({"":t},""):t}}}return e(function(){if(o)return u.exports;o=1;var e=a().stringify,r=h();return u.exports=function(t){return{parse:r(t),stringify:e}},u.exports.parse=r(),u.exports.stringify=e,u.exports}())}();

// ignore
export { JSONbig }
// ignore
